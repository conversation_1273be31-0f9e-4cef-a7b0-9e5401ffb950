<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="renderer" content="webkit">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
<link rel="stylesheet" href="/WebRes/Css/pintuer.css">
<link rel="stylesheet" href="/WebRes/Css/my.css">
<script src="/WebRes/Js/jquery.js"></script>
<script src="/WebRes/Js/pintuer.js"></script>
<script src="/WebRes/Js/jquery.SuperSlide.2.1.1.js"></script>
<script src="/WebRes/Js/layer.min.js"></script>
<script src="/WebRes/Js/core.js"></script>
<script src="/WebRes/Js/cookie.js"></script>
<script src="/WebRes/Js/respond.js"></script></head>
<body >
<div class="container margin-top">
<?php 
  include_once "../../WebRes/config.php";
  include_once "../../WebRes/security.php";
   if(!$_SESSION['accountName']) exit("<script>location.href='/index.php';</script>");
//先查询
$sql = $myliu->prepare("SELECT dj,name,game FROM account WHERE `name`=?");
$sql->bind_param('s', $_SESSION['accountName']);
$sql->execute();
$sql->bind_result($dj,$name,$game);
$SCDJ=0;
$user="";
$game2="";
while($sql->fetch()){
    $SCDJ=$dj;
    $user=$name;
    $game2=$game;
}
$sql->close();
?>
<br />
<br />
	<form class="form" id="DHYB">
		<p class="text-yellow text-center"><strong>玩家账号(account)：【&nbsp;<?php echo $user;?>&nbsp;】</strong>&nbsp;&nbsp;<strong>平台金币(gold)：【&nbsp;<?php echo $SCDJ;?>&nbsp;】</strong></p>
		<br />
		<br />
		<div class="form-group">
            <div class="label text-main"><label for="FQ">选择游戏分区(Game partition)</label></div>
            <div class="field padding-left">
            	<div class="button-group border-main radio">
				<?php  
				include_once "../../WebRes/config.php";
                //先查询
                $sql = $myliu->prepare("select fqid,a.name name from server a,account b WHERE a.server=b.game AND b.name=? order by a.fqid");
                $sql->bind_param('s', $_SESSION['accountName']);
                $sql->execute();
                $sql->bind_result($fqid,$name);

                while($sql->fetch()){
                    ?>
                    <label class="button margin-top" ><input name="FQ" onclick="Ajax_Get_GameUser('<?php echo $fqid ?>');" value="<?php echo  $fqid ?>"  type="radio" data-validate="radio:请选择游戏分区(Please select the game partition)"> <?php echo $name ?></label>

                    <?php
                }
                $sql->close();
                ?>


                </div>
            </div>
        </div><!--
        <div class="form-group">
            <div class="label text-main"><label for="Rid">选择对应分区帐号(số tài khoản)</label></div>
            <div class="field padding-left">
            	<div class="button-group border-main radio" id="S_GameUser">
	            	
            	</div>
            </div>
        </div>-->
        <div class="form-group">
            <div class="label"><label for="U_DH_Num">本次兑换金额，每次限制一亿金币(Each exchange restrictions within 10 million)</label></div>
            <div class="field padding-left">
            	<input type="text" class="input  text-center " onBlur = 'this.value = this.value.replace(/\D+/g, "")' onKeyPress = 'return /^\d$/.test(String.fromCharCode(event.keyCode))' id="U_DH_Num" name="U_DH_Num" size="20" oninput= 'this.value = this.value.replace(/\D+/g, "")'
onpropertychange='if(!/\D+/.test(this.value)){return;};this.value=this.value.replace(/\D+/g, "")' data-validate="required:请输入兑换数量!,plusinteger:请输入正整数!,compare#>0:数值须大于0,compare#<=100000001:数值须小于一亿或等于账户余额"/>
            </div>
        </div>	               
        <button class="button bg-main margin-bottom button-block" type="button" onclick="Ajax_DHYB('#DHYB')">确认兑换(exchange)</button>
		</form>
</div>
<script type="text/javascript">
	function Ajax_DHYB(FormID) {//ajax 兑换金币
		if (FormYz(FormID)){
			Tjdata=$(FormID).serialize();//序列化表单选项
			console.log(Tjdata);
			DoAjax("ajax.php?Action=DHYB",Tjdata,function (data) {
				if(data.status == 'y'){
					layer.alert(data.info, 1, function(){
					   window.location.reload();
					});	
				}else{
					layer.alert(data.info, 5, '温馨提示!');
				}
			});
		}
	}
	function Ajax_Get_GameUser(FQ) {//ajax 提取分区内角色
		Tjdata={FQ:FQ};//序列化表单选项
		var loadi = layer.load(0); 
		$("#S_GameUser").empty(); 
		DoAjax("ajax.php?Action=Get_GameUser",Tjdata,function (data) {
			
			if(data.status == 'y'){
				$("#S_GameUser").html(data.html);
				$("#THTj").attr("disabled",false); 
				$.getScript("/WebRes/js/pintuer.js");
			}else{
				$("#S_GameUser").html(data.html);
				$("#THTj").attr("disabled",true);  
			}
			layer.close(loadi);
		});
		
	}
	
	
 
</script>
</body>
</html>