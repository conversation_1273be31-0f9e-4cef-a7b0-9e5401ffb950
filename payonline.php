<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="renderer" content="webkit">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
<link rel="stylesheet" href="/WebRes/Css/pintuer.css">
<link rel="stylesheet" href="/WebRes/Css/my.css">
<script src="/WebRes/Js/jquery.js"></script>
<script src="/WebRes/Js/pintuer.js"></script>
<script src="/WebRes/Js/jquery.SuperSlide.2.1.1.js"></script>
<script src="/WebRes/Js/layer.min.js"></script>
<script src="/WebRes/Js/core.js"></script>
<script src="/WebRes/Js/cookie.js"></script>
<script src="/WebRes/Js/respond.js"></script></head>
<body >
<div class="container margin-top">
<?php 
  include_once "../../WebRes/config.php";
  include_once "../../WebRes/security.php";
   if(!$_SESSION['accountName']) exit("<script>location.href='/index.php';</script>");
//先查询
$sql = $myliu->prepare("SELECT dj,name,game FROM account WHERE `name`=?");
$sql->bind_param('s', $_SESSION['accountName']);
$sql->execute();
$sql->bind_result($dj,$name,$game);
$SCDJ=0;
$user="";
$game2="";
while($sql->fetch()){
    $SCDJ=$dj;
    $user=$name;
    $game2=$game;
}
$sql->close();


//加载积分商城数据
$reward = array();
$xml = simplexml_load_file('/home/<USER>/www/wudi/static/assets/xmls/xmls/chong.xml');


foreach($xml->children() as $period) {
    $reward[] = get_object_vars($period);
}

//解析物品
$info="";
foreach ($reward as $k=>$v) {
    $info=$v['@attributes']['desc'];
}



?>
<br />
<br />

		<p class="text-yellow text-center"><strong>玩家账号(account)：【&nbsp;<?php echo $user;?>&nbsp;】</strong>&nbsp;&nbsp;<strong>平台金币(gold)：【&nbsp;<?php echo $SCDJ;?>&nbsp;】</strong></p>
		<br />
		<br />
		<div class="form-group" id="main">

            <table>
                <tr>
                    <td>本次充值金额（最小1元)</td>

                </tr>
                <tr><td><br></td></tr>
                <tr>
                    <td><input type="text" class="input  text-center " width="200px" onBlur = 'this.value = this.value.replace(/\D+/g, "")' onKeyPress = 'return /^\d$/.test(String.fromCharCode(event.keyCode))' id="Money" name="Money" size="20" oninput= 'this.value = this.value.replace(/\D+/g, "")'
                               onpropertychange='if(!/\D+/.test(this.value)){return;};this.value=this.value.replace(/\D+/g, "");calDJ(this.value)' data-validate="required:请输入兑换数量!,plusinteger:请输入正整数!,compare#>0:数值须大于0,compare#<=*********:数值须小于一亿或等于账户余额"/></td>
                    <td><label id="djinfo" >  <?php echo $info; ?> </label></td>
                </tr>
                <tr><td><br></td></tr>
                <tr>
                    <td> <button class="button bg-main  " type="button"  onclick="Ajax_WX()">微信充值</button></td>
                    <td><button class="button bg-main " type="button" onclick="Ajax_ZFB()">支付宝充值</button></td>
                </tr>



            </table>

</div>
<script type="text/javascript">


	function Ajax_WX() {//ajax 兑换金币
           var gold = document.getElementById("Money").value;
           window.location.href="../../onlinepay/pay.php?gold="+gold;
	}

    function Ajax_ZFB() {//ajax 兑换金币
        var gold = document.getElementById("Money").value;
        window.location.href="../../onlinepay/payzfb.php?gold="+gold;
    }


</script>
</body>
</html>