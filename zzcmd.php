<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="renderer" content="webkit">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
<link rel="stylesheet" href="/WebRes/Css/pintuer.css">
<link rel="stylesheet" href="/WebRes/Css/my.css">
<script src="/WebRes/Js/jquery.js"></script>
<script src="/WebRes/Js/pintuer.js"></script>
<script src="/WebRes/Js/jquery.SuperSlide.2.1.1.js"></script>
<script src="/WebRes/Js/layer.min.js"></script>
<script src="/WebRes/Js/core.js"></script>
<script src="/WebRes/Js/cookie.js"></script>
<script src="/WebRes/Js/respond.js"></script></head>
<body >
<div class="container margin-top">
        <?php
        include_once "../../WebRes/config.php";
        include_once "../../WebRes/security.php";
        if(!$_SESSION['accountName']) exit("<script>location.href='/index.php';</script>");

        //先查询
        $sql = $myliu->prepare("SELECT dj,name,game FROM account WHERE `name`=?");
        $sql->bind_param('s', $_SESSION['accountName']);
        $sql->execute();
        $sql->bind_result($dj,$name,$game);
        $SCDJ=0;
        $user="";
        $game2="";
        while($sql->fetch()){
            $SCDJ=$dj;
            $user=$name;
            $game2=$game;
        }
        $sql->close();

        //加载积分商城数据
        $reward = array();
        $xml = simplexml_load_file('/home/<USER>/www/wudi/static/assets/xmls/xmls/caozi_shop.xml');
        foreach($xml->children() as $period) {
            $reward[] = get_object_vars($period);
        }

        //解析物品
        $allitemsinfo=array();

        $itemarray=array();
        foreach ($reward as $k=>$v) {
            //处理物品
            /* 没必要处理
           $itemtmp=$v['@attributes']['itemid'];
           $itemtmp1=explode(",",$itemtmp);
           foreach ($itemtmp1 as $item){
               $itemtmp2=  explode($item,":");
               $itemarray[$itemtmp2[0]]=$itemarray[$itemtmp2[1]];
           }*/
            //以上处理所有物品
            array_push($allitemsinfo,array('id'=>$v['@attributes']['id'],'itemid'=>$v['@attributes']['itemid'],'img'=>$v['@attributes']['img'],'itemid'=>$v['@attributes']['itemid'],'desc'=>$v['@attributes']['desc'],'score'=>$v['@attributes']['score'],'jifen'=>$v['@attributes']['jifen']));
        }


        $totaljf=0;


        ?>
        <form class="form" id="GMCZ">
            <p class="text-yellow text-left">注意：本活动游戏中不再重复累计超值额度，不累加积分</p>
            <p class="text-yellow text-left"><strong>玩家账号(account)：【&nbsp;<?php echo $user;?>&nbsp;】</strong>&nbsp;&nbsp;<strong>当前元宝：【&nbsp;<?php echo $SCDJ;?>&nbsp;】</strong></p>
            <div class="form-group">
                <div class="label text-main"><label for="FQ">选择游戏分区(Game partition)</label></div>
                <div class="field padding-left">
                    <div class="button-group border-main radio">
                        <?php
                        include_once "../../WebRes/config.php";


                        //先查询
                        $sql = $myliu->prepare("select fqid,a.name name from server a,account b WHERE a.server=b.game AND b.name=? order by a.fqid");
                        $sql->bind_param('s', $_SESSION['accountName']);
                        $sql->execute();
                        $sql->bind_result($fqid,$name);

                        while($sql->fetch()){
                            ?>
                        <label class="button margin-top" ><input name="FQ" onclick="Ajax_Get_GameUser('<?php echo $fqid ?>');" value="<?php echo  $fqid ?>"  type="radio" data-validate="radio:请选择游戏分区(Please select the game partition)"> <?php echo $name ?></label>

                       <?php
                        }
                                $sql->close();
                        ?>









                    </div>
                </div>
            </div>


            <div class="form-group">

                <table border="2">
                    <tr bgcolor="#ffebcd"> <td width="80px" align="center">选择</td><td width="80px" align="center">序号</td><td width="155px" align="center">物品礼包</td><td width="80px" align="center">描述</td><td width="80" align="center">元宝</td> <td width="75" align="center">返还积分</td></tr>
                    <?php
                    include_once "../../WebRes/config.php";

                    //$sql="select * from $database.server where game=".$game." order by fqid desc limit 0,10000";

                    foreach($allitemsinfo as $iteminfo)
                    {
                        ?>

                        <tr><td width="80px" align="center" valign="middle"><input type="radio"  name="radiobutton" onclick="SelectID('<?php echo $iteminfo['id']?>')"></td> <td width="80px" align="center" valign="middle"><?php echo $iteminfo['id']?></td>  <td width="80px" align="center" valign="middle"><img width="64" height="64" src="<?php echo $iteminfo['img']?>"></td>  <td width="155px" align="center" valign="middle"><?php echo $iteminfo['desc']?></td>  <td width="80px" align="center" valign="middle"><?php echo $iteminfo['score']?></td><td width="75px" align="center" valign="middle"><?php echo $iteminfo['jifen']?></td> </tr>


                    <?php }?>

                </table>
            </div>

            <input type="hidden"   id="U_DH_Num" name="U_DH_Num" value="0" size="20" />
            <input type="hidden"   id="jfid" name="jfid"  value="0" size="20" />

            <button class="button bg-main margin-bottom button-block" type="button" onclick="Ajax_DHJF('#GMCZ')">确认购买</button>
        </form>
    </div>


<script type="text/javascript">

    function SelectID(id){


        document.getElementById("jfid").value=id;
        //layer.alert(id, 1, '温馨提示!');
    }

    function Ajax_DHJF(FormID) {//ajax 兑换金币
        if (FormYz(FormID)){
            Tjdata=$(FormID).serialize();//序列化表单选项
            DoAjax("ajax.php?Action=GMCZ",Tjdata,function (data) {
                if(data.status == 'y'){
                    layer.alert(data.info, 1, function(){
                        window.location.reload();
                    });
                }else{
                    layer.alert(data.info, 5, '温馨提示!');
                }
            });
        }
    }
    function Ajax_Get_GameUser(FQ) {//ajax 提取分区内角色
        Tjdata={FQ:FQ};//序列化表单选项
        var loadi = layer.load(0);
        $("#S_GameUser").empty();
        DoAjax("ajax.php?Action=Get_GameUser",Tjdata,function (data) {

            if(data.status == 'y'){
                $("#S_GameUser").html(data.html);
                $("#THTj").attr("disabled",false);
                $.getScript("/WebRes/js/pintuer.js");
            }else{
                $("#S_GameUser").html(data.html);
                $("#THTj").attr("disabled",true);
            }
            layer.close(loadi);
        });

    }



</script>
</body>
</html>

</body>
</html>