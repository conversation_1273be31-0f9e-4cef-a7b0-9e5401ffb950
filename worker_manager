
#!/bin/bash

WORKER_DIR="/home/<USER>/rpcfw"
WORKER_SCRIPT="charge_worker.php"
PID_FILE="$WORKER_DIR/worker.pid"
LOG_FILE="$WORKER_DIR/worker_output.log"
PHP_BIN="/home/<USER>/bin/php"

cd $WORKER_DIR

case "$1" in
    start)
        if [ -f $PID_FILE ]; then
            PID=$(cat $PID_FILE)
            if ps -p $PID > /dev/null 2>&1; then
                echo "Charge worker is already running (PID: $PID)"
                exit 1
            else
                echo "Removing stale PID file"
                rm -f $PID_FILE
            fi
        fi
        
        echo "Starting charge worker..."
        nohup $PHP_BIN $WORKER_SCRIPT > $LOG_FILE 2>&1 &
        echo $! > $PID_FILE
        echo "Charge worker started with PID: $(cat $PID_FILE)"
        ;;
        
    stop)
        if [ -f $PID_FILE ]; then
            PID=$(cat $PID_FILE)
            echo "Stopping charge worker (PID: $PID)..."
            kill $PID
            rm -f $PID_FILE
            echo "Charge worker stopped"
        else
            echo "Charge worker is not running"
        fi
        ;;
        
    restart)
        $0 stop
        sleep 2
        $0 start
        ;;
        
    status)
        if [ -f $PID_FILE ]; then
            PID=$(cat $PID_FILE)
            if ps -p $PID > /dev/null 2>&1; then
                echo "Charge worker is running (PID: $PID)"
                echo "Log file: $LOG_FILE"
                echo "Queue status:"
                echo "  Pending: $(ls $WORKER_DIR/queue/*.json 2>/dev/null | wc -l)"
                echo "  Processing: $(ls $WORKER_DIR/processing/*.json 2>/dev/null | wc -l)"
                echo "  Completed: $(ls $WORKER_DIR/completed/*.json 2>/dev/null | wc -l)"
                echo "  Failed: $(ls $WORKER_DIR/failed/*.json 2>/dev/null | wc -l)"
            else
                echo "Charge worker is not running (stale PID file)"
                rm -f $PID_FILE
            fi
        else
            echo "Charge worker is not running"
        fi
        ;;
        
    log)
        if [ -f $LOG_FILE ]; then
            tail -f $LOG_FILE
        else
            echo "Log file not found: $LOG_FILE"
        fi
        ;;
        
    monitor)
        echo "=== Charge Worker Monitor ==="
        $0 status
        echo ""
        echo "Recent charge results:"
        if [ -f "$WORKER_DIR/charge_results.log" ]; then
            tail -5 "$WORKER_DIR/charge_results.log" | while read line; do
                echo "  $line"
            done
        fi
        ;;
        
    *)
        echo "Usage: $0 {start|stop|restart|status|log|monitor}"
        echo ""
        echo "Commands:"
        echo "  start    - Start the charge worker"
        echo "  stop     - Stop the charge worker"
        echo "  restart  - Restart the charge worker"
        echo "  status   - Show worker status and queue statistics"
        echo "  log      - Show real-time log output"
        echo "  monitor  - Show status and recent results"
        exit 1
        ;;
esac
EOF

# 设置执行权限
chmod +x /home/<USER>/rpcfw/worker_manager.sh