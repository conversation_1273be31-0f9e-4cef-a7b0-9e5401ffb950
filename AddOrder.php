<?php
/***************************************************************************
 * 
 * Copyright (c) 2011 babeltime.com, Inc. All Rights Reserved
 * $Id: AddOrder.php 25439 2012-08-09 10:57:50Z HongyuLan $
 * 
 **************************************************************************/

 /**
 * @file $HeadURL: svn://************:3698/C/tags/pirate/rpcfw/rpcfw_1-0-21-57/test/AddOrder.php $
 * <AUTHOR> HongyuLan $(<EMAIL>)
 * @date $Date: 2012-08-09 18:57:50 +0800 (四, 2012-08-09) $
 * @version $Revision: 25439 $
 * @brief 
 *  
 **/

/**
 * 用法： btscript GoldModifyTest.php uid gold
 * gold 表示设置为多少金币
 * Enter description here ...
 * <AUTHOR>
 *
 */

class AddOrder extends BaseScript
{
	/* (non-PHPdoc)
	 * @see BaseScript::executeScript()
	 */
	protected function executeScript($arrOption)
	{
		if (count($arrOption) != 5)
		{
			exit('argv err.');
		}

      //  {$uid} {$order_id} {$gold} 0

		
		$uid = $arrOption[0];
		$orderId = $arrOption[1];
		$gold = $arrOption[2];
        $acc = $arrOption[3];
		$exGold = $arrOption[4];


        //连接数据库方便操作
        $myliu = new mysqli("127.0.0.1","pirate","admin","zdgdse_byde_kkydb");
        $myliu->query('set names utf8');

        //先查询
        $sql = $myliu->prepare("SELECT dj,jifen FROM account WHERE `name`=?");
        $sql->bind_param('s', $acc);
        $sql->execute();
        $sql->bind_result($dj,$jifen);
        $dj2=0;
        $jifen2=0;
        while($sql->fetch()){
            $dj2=$dj;
            $jifen2=$jifen;
        }
        $sql->close();


        if($gold > $dj2 || $gold<=0 ){

            $sql = $myliu->prepare("update account set userlock=1 WHERE `name`=?");
            $sql->bind_param('s', $acc);
            $sql->execute();
            error_log(UTIL::getTime()."**超值封想充".$gold."当前".$dj2."信息=".print_r($arrOption,1)."\n",3,"/home/<USER>/rpcfw/WEB_BUY_INFO.txt");
            echo "err\n";

        }else{

            //更新元宝加积分
            $time=date("Y-m-d H:i:s");
            // $sql2=$myliu->prepare("update account set dj=dj-'".$gold."',jifen=jifen+'".$gold."',payj=payj+'".$gold."' where name =?");
            $sql2=$myliu->prepare("update account set dj=dj-'".$gold."',payj=payj+'".$gold."' where name =?");
            $sql2->bind_param('s', $acc);
            $sql2->execute();
            $sql2->close();
            //插入日志
            $sql3 = $myliu->prepare("insert into pay_info (paytouser,paygold,paytime,chrname) values (?,?,?,?)");
            $sql3->bind_param('siss', $acc,$gold,$time,$acc);
            $sql3->execute();
            $sql3->close();


            RPCContext::getInstance()->executeTask($uid, 'user.addGold4BBpay', array($uid, $orderId, $gold, $exGold));
            Logger::info('add order:%s', $orderId);

            echo "ok\n";

        }


	}
}

/* vim: set ts=4 sw=4 sts=4 tw=100 noet: */