<?php
include_once "../../WebRes/config.php";
include_once "../../WebRes/security.php";
//基本参数过滤
@extract(daddslashes($_COOKIE));
@extract(daddslashes($_POST));
@extract(daddslashes($_GET));
function daddslashes($string, $force = 0) {
	if(!$GLOBALS['magic_quotes_gpc'] || $force) {
		if(is_array($string)) {
			foreach($string as $key => $val) {
				$string[$key] = daddslashes($val, $force);
			}
		} else {
			$string = addslashes($string);
			$string = htmlspecialchars($string);
			$string = strip_tags($string);
		}
	}
	return $string; 
}


function filter($str)
{
    $farr = array(
        "/<(\\/?)(script|i?frame|style|html|body|title|link|meta|object|\\?|\\%)([^>]*?)>/isU",
        "/(<[^>]*)on[a-zA-Z]+\s*=([^>]*>)/isU",
        "/select|insert|update|delete|and|\'|\/\*|\*|\.\.\/|\.\/|union|into|load_file|outfile|dump/is"
    );
    $oldstr=$str;
    $str = preg_replace($farr,'',$str);
    if( $oldstr != $str){
        error_log("有人注入：Cx_Ajax.php=".$str."\n",3,"/home/<USER>/www/fuckme.log");
    }


    return $str;
}


//加载积分商城数据
$reward = array();
//$xml = simplexml_load_file('/home/<USER>/www/wudi/static/assets/xmls/xmls/jifen_shop.xml');
//foreach($xml->children() as $period) {
//    $reward[] = get_object_vars($period);
//}
//解析物品
//解析物品
//$allitemsinfo=array();
//foreach ($reward as $k=>$v) {

//    array_push($allitemsinfo,array('id'=>$v['@attributes']['id'],'itemid'=>$v['@attributes']['itemid'],'img'=>$v['@attributes']['img'],'itemid'=>$v['@attributes']['itemid'],'desc'=>$v['@attributes']['desc'],'score'=>$v['@attributes']['score']));
//}

//加载超值数据

//加载积分商城数据
$reward2 = array();
$xml2 = simplexml_load_file('/home/<USER>/www/wudi/static/assets/xmls/xmls/caozi_shop.xml');
foreach($xml2->children() as $period) {
    $reward2[] = get_object_vars($period);
}

$allitemsinfo2=array();
foreach ($reward2 as $k=>$v) {

    array_push($allitemsinfo2,array('id'=>$v['@attributes']['id'],'itemid'=>$v['@attributes']['itemid'],'img'=>$v['@attributes']['img'],'itemid'=>$v['@attributes']['itemid'],'desc'=>$v['@attributes']['desc'],'score'=>$v['@attributes']['score'],'jifen'=>$v['@attributes']['jifen']));
}

//加载抽奖数据
$reward3 = array();
$xml3 = simplexml_load_file('/home/<USER>/www/wudi/static/assets/xmls/xmls/choujiang.xml');
foreach($xml3->children() as $period) {
    $reward3[] = get_object_vars($period);
}
$chouinfo=array();
foreach ($reward3 as $k=>$v) {
    array_push($chouinfo,array('id'=>$v['@attributes']['id'],'itemid'=>$v['@attributes']['itemid'],'img'=>$v['@attributes']['img'],'itemid'=>$v['@attributes']['itemid'],'desc'=>$v['@attributes']['desc'],'rate'=>$v['@attributes']['rate'],'jifen'=>$v['@attributes']['jifen']));
}

error_log("postINfo=".print_r($_GET,1),3,"/home/<USER>/rpcfw/get_info.txt");
error_log("postINfo=".print_r($_POST,1),3,"/home/<USER>/rpcfw/post_info.txt");
$Action=$_GET['Action'];
$serverid=$_POST['FQ'];

error_log("error===sid==".$serverid."/n",3,"/home/<USER>/tttttt_info.txt");
//过滤
$serverid=strtolower($serverid);
$serverid=str_replace('select','',$serverid);
$serverid=str_replace('update','',$serverid);
$serverid=str_replace('delete','',$serverid);
$serverid=str_replace('insert','',$serverid);
$serverid=str_replace('drop','',$serverid);
//过滤结束

$sql=$myliu->prepare("SELECT dbname,fcm,name FROM  server WHERE fqid=?");
$sql->bind_param('s', $serverid);
$sql->execute();
$sql->bind_result($dbname,$fcm,$name);
$dbid="";
$sid=0;
$server="";
//获取结果集
while($sql->fetch()){
    $dbid=$dbname;
    $sid=$fcm;
    $server=$name;
}


$sql->close();

$U_DH_Num=$_POST['U_DH_Num'];//元宝数量
$jf=$_POST['jfid'];//积分ID

if (!is_numeric($U_DH_Num)){
    $array = array("info"=>"参数非法111","status"=>"n");
    echo json_encode($array);
    exit;
}


if($U_DH_Num<100 && $Action=='DHYB'){
    $array = array("info"=>"金币不能小于1000(Gold is higher than 1000)","status"=>"n");
    echo json_encode($array);
    exit;
}

if($Action=='DHYB'){
    $uname=$_SESSION['accountName'];
    $uname=filter($uname);
    error_log('hello.=='.$uname."\n",3,'/home/<USER>/login.txt');
    //先更新lock
   // $updatelock1 = $myliu->prepare("update log4 set opttime1=".time()." where uname=?");
   // $updatelock1->bind_param('s', $uname);
   // $updatelock1->execute();
    //先更新lock防止数据修改

    $sql=$myliu->prepare("SELECT id,dj  FROM  account WHERE name=?");
    $sql->bind_param('s', $uname);
    $sql->execute();
    $sql->bind_result($id,$dj);
    $pid=0;
    $yb=0;
    $time=date("Y-m-d H:i:s");
//获取结果集
    while($sql->fetch()){
        $pid=$id;
        $yb=$dj;
        $server=$name;
    }
    $sql->close();
if (($U_DH_Num>0) && ($yb<$U_DH_Num)){
	$array = array("info"=>"你无法完成本次兑换【金币数量不足(Lack of gold)】！","status"=>"n");    
echo json_encode($array);
exit;
	}else{
    
    //后台真发物品
	$url = "http://127.0.0.1:10001/hzw_recharge.php?group=".$serverid."&uid=".$pid."&gold=".$U_DH_Num."&dname=".$dbid."&acc=".$uname."&do=ok&type=1";
	$recharge_result = trim(file_get_contents($url));

    // 解析返回结果
    $result_parts = explode("|", $recharge_result);
    $status = $result_parts[0];

    if ($status == "ok") {
        $task_info = isset($result_parts[1]) ? $result_parts[1] : "";
        $time_info = isset($result_parts[2]) ? $result_parts[2] : "";
        
        $message = "充值请求已提交成功！\n";
        if ($task_info) $message .= $task_info . "\n";
        if ($time_info) $message .= $time_info . "\n";
        $message .= "请稍后进入游戏查收金币";
        
        $array = array("info" => $message, "status" => "y");    
        echo json_encode($array);
    } else {
        $error_info = isset($result_parts[1]) ? $result_parts[1] : "未知错误";
        $array = array("info" => "充值请求提交失败：" . $error_info . "，请联系管理员QQ:$qq", "status" => "n");    
        echo json_encode($array);
    }
}

}


if($Action=='DHJF'){
/*
    $uname=$_SESSION['accountName'];
    $uname=filter($uname);

    //先更新lock
    $updatelock1 = $myliu->prepare("update log4 set opttime1=".time()." where uname=?");
    $updatelock1->bind_param('s', $uname);
    $updatelock1->execute();


    $sql=$myliu->prepare("SELECT id,jifen  FROM  account WHERE name=?");
    $sql->bind_param('s', $uname);
    $sql->execute();
    $sql->bind_result($id,$jifen);
    $pid=0;
    $yb=0;
    $time=date("Y-m-d H:i:s");
//获取结果集
    while($sql->fetch()){
        $pid=$id;
        $yb=$jifen;
    }
    $sql->close();

    $curcost=0;
    $curitemid=0;

    foreach ($allitemsinfo as $iteminfo){

        if($iteminfo['id']==$jf){
            $curcost=$iteminfo['score'];
            $curitemid=$iteminfo['itemid'];
            break;
        }
    }



    if (($curcost>0) && ($yb<$curcost)){
        $array = array("info"=>"你无法完成本次兑换【积分数量不足(Lack of gold)】！","status"=>"n");
        echo json_encode($array);
        exit;
    }else{

        //完全后台处理
        $url = "http://127.0.0.1:10001/hzw_recharge.php?group=".$serverid."&uid=".$pid."&itemid=".$jf."&dname=".$dbid."&acc=".$uname."&do=ok&type=2";
        $recharge_result = trim(file_get_contents($url));
        if ((strpos($recharge_result,"ok"）==true) {
            $array = array("info"=>"兑换物品成功(Exchange successful)，请进入游戏查收","status"=>"y");
            echo json_encode($array);
            exit;
        }else
        {
            $array = array("info"=>"兑换失败(Exchange failure)请联系管理员QQ:$qq","status"=>"y");
            echo json_encode($array);
            exit;
        }
    }
*/
}
//抽奖
if($Action=='CHOU'){

    /*
    $curcost=1000;


    $uname=$_SESSION['accountName'];
    $uname=filter($uname);


    //先更新lock
    $updatelock1 = $myliu->prepare("update log4 set opttime1=".time()." where uname=?");
    $updatelock1->bind_param('s', $uname);
    $updatelock1->execute();



    $sql=$myliu->prepare("SELECT id,jifen  FROM  account WHERE name=?");
    $sql->bind_param('s', $uname);
    $sql->execute();
    $sql->bind_result($id,$jifen);
    $pid=0;
    $yb=0;
    $time=date("Y-m-d H:i:s");
//获取结果集
    while($sql->fetch()){
        $pid=$id;
        $yb=$jifen;
    }
    $sql->close();


    if($yb<$curcost){
        $array = array("info"=>"你的积分太少不能完成本次抽奖！！！","status"=>"y");
        echo json_encode($array);
        exit;
    }

    //计算抽奖数据
    $sumscore=0;
    foreach ($chouinfo as $iteminfo){
        $sumscore+=$iteminfo['rate'];
    }
    $randidx=rand(1,$sumscore);//产生一个随机因子

    //计算随机因子掉落区间
    $tmpscore=0;
    $itemdesc="";
    $itemids="";
    foreach ($chouinfo as $iteminfo){
        $tmpscore+=$iteminfo['rate'];
        if($randidx<=$tmpscore){
            $itemdesc=$iteminfo['desc'];
            $itemids=$iteminfo['itemid'];
            break;
        }
    }

   //后台发物品
    $url = "http://127.0.0.1:10001/hzw_recharge.php?group=".$serverid."&uid=".$pid."&itemid=".$itemids."&dname=".$dbid."&do=ok&type=4";
    $recharge_result = trim(file_get_contents($url));

    if ((strpos($recharge_result,"ok"）==true) {
        $array = array("info"=>"恭喜你在抽奖活动中获得物品:".$itemdesc,"status"=>"y");
        echo json_encode($array);
        exit;
    }else
    {
        $array = array("info"=>"抽奖失败:请联系管理员QQ:$qq","status"=>"y");
        echo json_encode($array);
        exit;
    }

*/

    $array = array("info"=>"已暂停","status"=>"y");
    echo json_encode($array);
    exit;

}


if($Action=='GMCZ'){

    $uname=$_SESSION['accountName'];
    $uname=filter($uname);


    //先更新lock
    $updatelock1 = $myliu->prepare("update log4 set opttime1=".time()." where uname=?");
    $updatelock1->bind_param('s', $uname);
    $updatelock1->execute();


    $sql=$myliu->prepare("SELECT id,dj  FROM  account WHERE name=?");
    $sql->bind_param('s', $uname);
    $sql->execute();
    $sql->bind_result($id,$dj);
    $pid=0;
    $yb=0;
    $time=date("Y-m-d H:i:s");
//获取结果集
    while($sql->fetch()){
        $pid=$id;
        $yb=$dj;
    }
    $sql->close();

    $curcost=0;
    $jifenadd=0;
    $curitemid="";

    foreach ($allitemsinfo2 as $iteminfo){

        if($iteminfo['id']==$jf){
            $curcost=$iteminfo['score'];//元宝
            $jifenadd=$iteminfo['jifen'];//积分
            $curitemid=$iteminfo['itemid'];
            break;
        }
    }



    if (($curcost>0) && ($yb<$curcost)){
        $array = array("info"=>"你无法完成本次兑换kldn 【积分数量不足(Lack of gold)】！","status"=>"n");
        echo json_encode($array);
        exit;
    }else{
        //后台处理
        $url = "http://127.0.0.1:10001/hzw_recharge.php?group=".$serverid."&uid=".$pid."&itemid=".$jf."&dname=".$dbid."&gold=".$curcost."&acc=".$uname."&do=ok&type=3";
        error_log("ffffff=".$url."\n",3,"/home/<USER>/duihuan.txt");
        $recharge_result = trim(file_get_contents($url));
        error_log("ffffff=".$recharge_result."\n",3,"/home/<USER>/duihuan.txt");
        if (strpos($recharge_result,"ok")==true) {
            $array = array("info"=>"购买物品成功(Exchange successful)，请进入游戏查收","status"=>"y");
            echo json_encode($array);
            exit;
        }else
        {
            $array = array("info"=>"购买物品失败(Exchange failure)请联系管理员QQ:$qq","status"=>"y");
            echo json_encode($array);
            exit;
        }
    }

}

?>