<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="renderer" content="webkit">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
<link rel="stylesheet" href="/WebRes/Css/pintuer.css">
<link rel="stylesheet" href="/WebRes/Css/my.css">
<script src="/WebRes/Js/jquery.js"></script>
<script src="/WebRes/Js/pintuer.js"></script>
<script src="/WebRes/Js/jquery.SuperSlide.2.1.1.js"></script>
<script src="/WebRes/Js/layer.min.js"></script>
<script src="/WebRes/Js/core.js"></script>
<script src="/WebRes/Js/cookie.js"></script>
<script src="/WebRes/Js/respond.js"></script></head>
<body >
<div class="container margin-top">
    <?php
    include_once "../../WebRes/config.php";
    include_once "../../WebRes/security.php";
    if(!$_SESSION['accountName']) exit("<script>location.href='/index.php';</script>");
    //先查询
    $sql = $myliu->prepare("SELECT jifen,name,game FROM account WHERE `name`=?");
    $sql->bind_param('s', $_SESSION['accountName']);
    $sql->execute();
    $sql->bind_result($jifen,$name,$game);
    $SCDJ=0;
    $user="";
    $game2="";
    while($sql->fetch()){
        $SCDJ=$jifen;
        $user=$name;
        $game2=$game;
    }
    $sql->close();

    //加载积分商城数据
    $reward = array();
    $xml = simplexml_load_file('/home/<USER>/www/wudi/static/assets/xmls/xmls/choujiang.xml');
    foreach($xml->children() as $period) {
        $reward[] = get_object_vars($period);
    }

    //解析物品
    $allitemsinfo=array();
    foreach ($reward as $k=>$v) {

        array_push($allitemsinfo,array('id'=>$v['@attributes']['id'],'itemid'=>$v['@attributes']['itemid'],'img'=>$v['@attributes']['img'],'itemid'=>$v['@attributes']['itemid'],'desc'=>$v['@attributes']['desc'],'rate'=>$v['@attributes']['rate']));
    }


    $totaljf=0;


    ?>
    <form class="form" id="CHOU">
        <p class="text-yellow text-left"><strong>玩家账号(account)：【&nbsp;<?php echo $user;?>&nbsp;】</strong>&nbsp;&nbsp;<strong>当前积分：【&nbsp;<?php echo $SCDJ;?>&nbsp;】</strong></p>
        <div class="form-group">
            <div class="label text-main"><label for="FQ">选择游戏分区(Game partition)</label></div>
            <div class="field padding-left">
                <div class="button-group border-main radio">
                    <?php
                    include_once "../../WebRes/config.php";
                    //先查询
                    $sql = $myliu->prepare("select fqid,a.name name from server a,account b WHERE a.server=b.game AND b.name=? order by a.fqid");
                    $sql->bind_param('s', $_SESSION['accountName']);
                    $sql->execute();
                    $sql->bind_result($fqid,$name);

                    while($sql->fetch()){
                        ?>
                        <label class="button margin-top" ><input name="FQ" onclick="Ajax_Get_GameUser('<?php echo $fqid ?>');" value="<?php echo  $fqid ?>"  type="radio" data-validate="radio:请选择游戏分区(Please select the game partition)"> <?php echo $name ?></label>

                        <?php
                    }
                    $sql->close();
                    ?>
                </div>
            </div>
        </div>

        玩法说明：1000积分参与一次抽奖哦~~~每个物品有不同的中奖概率！
        <div class="form-group">




            <table border="1px" cellspacing="0">
                <?php
                include_once "../../WebRes/config.php";



                //$sql="select * from $database.server where game=".$game." order by fqid desc limit 0,10000";

                for ($i=0;$i<count($allitemsinfo);$i+=4)
                {

                    ?>

                    <tr>
                        <?php if(!empty( $allitemsinfo[$i])){?>
                        <td width="155px" align="center" valign="middle" bgcolor="#eee8aa"><img width="64" height="64" src="<?php echo $allitemsinfo[$i]['img']?>"><br><?php echo $allitemsinfo[$i]['desc']?></td>
                        <?php }?>
                        <?php if(!empty( $allitemsinfo[$i+1])){?>
                        <td width="155px" align="center" valign="middle" bgcolor="#eee8aa"><img width="64" height="64" src="<?php echo $allitemsinfo[$i+1]['img']?>"><br><?php echo $allitemsinfo[$i+1]['desc']?></td>
                        <?php }?>
                        <?php if(!empty( $allitemsinfo[$i+2])){?>
                        <td width="155px" align="center" valign="middle" bgcolor="#eee8aa"><img width="64" height="64" src="<?php echo $allitemsinfo[$i+2]['img']?>"><br><?php echo $allitemsinfo[$i+2]['desc']?></td>
                        <?php }?>
                        <?php if(!empty( $allitemsinfo[$i+3])){?>
                        <td width="155px" align="center" valign="middle" bgcolor="#eee8aa"><img width="64" height="64" src="<?php echo $allitemsinfo[$i+3]['img']?>"><br><?php echo $allitemsinfo[$i+3]['desc']?></td>
                        <?php }?>
                    </tr>

                <?php }?>
                </table>

        </div>

        <input type="hidden"   id="U_DH_Num" name="U_DH_Num" value="0" size="20" />
        <input type="hidden"   id="jfid" name="jfid"  value="0" size="20" />
        <label id="infomess">中奖信息:</label>
        <button class="button bg-main margin-bottom button-block" type="button" onclick="Ajax_CHOU('#CHOU')">开始抽奖</button>
    </form>
</div>
<script type="text/javascript">


    function Ajax_CHOU(FormID) {//ajax 兑换金币
        if (FormYz(FormID)){
            Tjdata=$(FormID).serialize();//序列化表单选项
            DoAjax("ajax.php?Action=CHOU",Tjdata,function (data) {
                if(data.status == 'y'){
                   // layer.alert(data.info, 1, function(){
                      //  window.location.reload();
                   // });
                   //
                    document.getElementById("infomess").innerText= data.info;
                }else{
                    //layer.alert(data.info, 5, '温馨提示!');
                    document.getElementById("infomess").innerText= data.info;
                }
            });
        }
    }
    function Ajax_Get_GameUser(FQ) {//ajax 提取分区内角色
        Tjdata={FQ:FQ};//序列化表单选项
        var loadi = layer.load(0);
        $("#S_GameUser").empty();
        DoAjax("ajax.php?Action=Get_GameUser",Tjdata,function (data) {

            if(data.status == 'y'){
                $("#S_GameUser").html(data.html);
                $("#THTj").attr("disabled",false);
                $.getScript("/WebRes/js/pintuer.js");
            }else{
                $("#S_GameUser").html(data.html);
                $("#THTj").attr("disabled",true);
            }
            layer.close(loadi);
        });

    }



</script>
</body>
</html>